/**
 * EntityController Auto-Upgrade Function
 * Automatically upgrades all selected EntityController instances to max level
 * Uses frida-il2cpp-bridge for Unity IL2CPP method hooking
 */

class EntityAutoUpgrader {
    constructor() {
        this.assemblyImage = null;
        this.entityControllerClass = null;
        this.isInitialized = false;
        this.useRvaFallback = false;
        this.rvaAddresses = {
            IsSelected: 0x1E53050,
            CanUpgrade: 0x1E4027C,
            GetMaxUpgradeLevel: 0x1E4A0B8,
            // Add other RVA addresses as needed
            GetLevel: null, // Will try to find dynamically
            InstantUpgrade: null // Will try to find dynamically
        };
        this.stats = {
            totalProcessed: 0,
            selectedEntities: 0,
            upgradableEntities: 0,
            upgradesPerformed: 0,
            errors: 0
        };
    }

    /**
     * Initialize Il2Cpp domain and get EntityController class
     */
    async initialize() {
        try {
            console.log("🔧 Initializing EntityController Auto-Upgrader...");

            // Get Assembly-CSharp image using frida-il2cpp-bridge
            this.assemblyImage = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            if (!this.assemblyImage) {
                console.log("❌ Failed to get Assembly-CSharp image");
                return false;
            }

            // Get EntityController class
            this.entityControllerClass = this.assemblyImage.class("EntityController");
            if (!this.entityControllerClass) {
                console.log("❌ EntityController class not found");
                return false;
            }

            console.log("✅ EntityController class found");

            // Test if normal method calls work, enable RVA fallback if needed
            this.testMethodAccess();

            this.isInitialized = true;
            return true;

        } catch (error) {
            console.log(`❌ Initialization failed: ${error}`);
            return false;
        }
    }

    /**
     * Test method access and enable RVA fallback if needed
     */
    testMethodAccess() {
        try {
            // Try to get some instances to test method access
            const testInstances = Il2Cpp.gc.choose(this.entityControllerClass);
            if (testInstances && testInstances.length > 0) {
                const testInstance = testInstances[0];

                // Test if we can access methods normally
                try {
                    const method = testInstance.method("IsSelected");
                    if (!method) {
                        console.log("⚠️ Normal method access failed, enabling RVA fallback");
                        this.useRvaFallback = true;
                    } else {
                        console.log("✅ Normal method access working");
                    }
                } catch (error) {
                    console.log("⚠️ Method access test failed, enabling RVA fallback");
                    this.useRvaFallback = true;
                }
            }
        } catch (error) {
            console.log(`⚠️ Method access test error: ${error}`);
            this.useRvaFallback = true;
        }
    }

    /**
     * Enable or disable RVA fallback mode
     */
    setRvaFallback(enabled) {
        this.useRvaFallback = enabled;
        console.log(`🔧 RVA fallback mode: ${enabled ? 'ENABLED' : 'DISABLED'}`);
    }

    /**
     * Safely invoke a method on an instance with error handling
     * Includes RVA fallback for known methods
     */
    safeInvoke(instance, methodName, ...args) {
        try {
            if (!instance || !instance.method) {
                return { error: "Invalid instance", value: null };
            }

            // Try normal method invocation first
            try {
                const method = instance.method(methodName);
                if (method) {
                    const result = method.invoke(...args);
                    return { error: null, value: result };
                }
            } catch (methodError) {
                console.log(`⚠️ Normal method call failed for ${methodName}, trying RVA fallback...`);
            }

            // Fallback to RVA-based method calling if available
            if (this.rvaAddresses[methodName] && this.useRvaFallback) {
                return this.invokeByRva(instance, methodName, ...args);
            }

            return { error: `Method ${methodName} not found`, value: null };

        } catch (error) {
            const errorMsg = String(error);
            if (errorMsg.includes("access violation") || errorMsg.includes("0x0")) {
                return { error: "Access violation - invalid instance", value: null };
            }
            return { error: `Method error: ${errorMsg}`, value: null };
        }
    }

    /**
     * Invoke method using RVA address (fallback method)
     */
    invokeByRva(instance, methodName, ...args) {
        try {
            const rvaAddress = this.rvaAddresses[methodName];
            if (!rvaAddress) {
                return { error: `No RVA address for ${methodName}`, value: null };
            }

            // Get the base address of the game module
            const gameModule = Process.findModuleByName("GameAssembly.dll");
            if (!gameModule) {
                return { error: "GameAssembly.dll not found", value: null };
            }

            const methodAddress = gameModule.base.add(rvaAddress);
            const nativeFunction = new NativeFunction(methodAddress, 'pointer', ['pointer', ...args.map(() => 'pointer')]);

            const result = nativeFunction(instance.handle, ...args);
            return { error: null, value: result };

        } catch (error) {
            return { error: `RVA invocation failed: ${error}`, value: null };
        }
    }

    /**
     * Check if an EntityController instance is selected
     */
    isSelected(instance) {
        const result = this.safeInvoke(instance, "IsSelected");
        if (result.error) {
            return false;
        }
        return result.value === true;
    }

    /**
     * Check if an EntityController instance can be upgraded
     */
    canUpgrade(instance, useAlternateResource = false) {
        const result = this.safeInvoke(instance, "CanUpgrade", useAlternateResource);
        if (result.error) {
            return false;
        }
        return result.value === true;
    }

    /**
     * Get current level of an EntityController instance
     */
    getLevel(instance) {
        const result = this.safeInvoke(instance, "GetLevel");
        if (result.error) {
            return -1;
        }
        return result.value || 0;
    }

    /**
     * Get maximum level of an EntityController instance
     */
    getMaxLevel(instance) {
        // Try GetMaxLevel first, then GetMaxUpgradeLevel as fallback
        let result = this.safeInvoke(instance, "GetMaxLevel");
        if (result.error) {
            result = this.safeInvoke(instance, "GetMaxUpgradeLevel");
        }
        if (result.error) {
            return -1;
        }
        return result.value || 0;
    }

    /**
     * Perform instant upgrade on an EntityController instance
     */
    instantUpgrade(instance) {
        const result = this.safeInvoke(instance, "InstantUpgrade");
        return !result.error;
    }

    /**
     * Process a single EntityController instance for auto-upgrade
     */
    async processInstance(instance, index) {
        try {
            // Check if instance is selected
            if (!this.isSelected(instance)) {
                return { processed: false, reason: "Not selected" };
            }
            this.stats.selectedEntities++;

            // Check if instance can be upgraded
            if (!this.canUpgrade(instance)) {
                return { processed: false, reason: "Cannot upgrade" };
            }
            this.stats.upgradableEntities++;

            const currentLevel = this.getLevel(instance);
            const maxLevel = this.getMaxLevel(instance);

            if (currentLevel === -1 || maxLevel === -1) {
                return { processed: false, reason: "Could not get level info" };
            }

            if (currentLevel >= maxLevel) {
                return { processed: false, reason: `Already at max level (${currentLevel}/${maxLevel})` };
            }

            console.log(`🔧 [${index}] Upgrading entity from level ${currentLevel} to ${maxLevel}...`);

            // Perform upgrades until max level is reached
            let upgradesPerformed = 0;
            let currentLevelCheck = currentLevel;

            while (currentLevelCheck < maxLevel && upgradesPerformed < 50) { // Safety limit
                if (!this.canUpgrade(instance)) {
                    break; // Can't upgrade anymore
                }

                if (this.instantUpgrade(instance)) {
                    upgradesPerformed++;
                    this.stats.upgradesPerformed++;
                    
                    // Wait a bit between upgrades
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                    // Check new level
                    currentLevelCheck = this.getLevel(instance);
                    if (currentLevelCheck === -1) {
                        break; // Error getting level
                    }
                } else {
                    break; // Upgrade failed
                }
            }

            const finalLevel = this.getLevel(instance);
            console.log(`✅ [${index}] Upgraded entity: ${currentLevel} → ${finalLevel} (${upgradesPerformed} upgrades)`);

            return { 
                processed: true, 
                reason: `Upgraded ${upgradesPerformed} times`,
                initialLevel: currentLevel,
                finalLevel: finalLevel,
                upgradesPerformed: upgradesPerformed
            };

        } catch (error) {
            this.stats.errors++;
            return { processed: false, reason: `Error: ${error}` };
        }
    }

    /**
     * Main auto-upgrade function - processes all EntityController instances
     */
    async autoUpgradeAll() {
        if (!this.isInitialized) {
            console.log("❌ Auto-upgrader not initialized. Call initialize() first.");
            return false;
        }

        try {
            console.log("🚀 Starting EntityController auto-upgrade process...");

            // Reset stats
            this.stats = {
                totalProcessed: 0,
                selectedEntities: 0,
                upgradableEntities: 0,
                upgradesPerformed: 0,
                errors: 0
            };

            // Get all EntityController instances
            const entityInstances = Il2Cpp.gc.choose(this.entityControllerClass);
            
            if (!entityInstances || entityInstances.length === 0) {
                console.log("❌ No EntityController instances found");
                return false;
            }

            console.log(`🔍 Found ${entityInstances.length} EntityController instances`);

            // Process each instance
            for (let i = 0; i < entityInstances.length; i++) {
                const instance = entityInstances[i];
                this.stats.totalProcessed++;

                const result = await this.processInstance(instance, i);
                
                if (result.processed) {
                    console.log(`✅ [${i}] ${result.reason}`);
                } else if (result.reason !== "Not selected") {
                    // Only log non-selected entities if there are very few
                    if (this.stats.selectedEntities < 10) {
                        console.log(`⏭️ [${i}] Skipped: ${result.reason}`);
                    }
                }

                // Small delay between instances to avoid overwhelming the game
                if (i % 10 === 0 && i > 0) {
                    await new Promise(resolve => setTimeout(resolve, 50));
                }
            }

            // Print final statistics
            console.log("\n📊 Auto-Upgrade Statistics:");
            console.log(`   Total Processed: ${this.stats.totalProcessed}`);
            console.log(`   Selected Entities: ${this.stats.selectedEntities}`);
            console.log(`   Upgradable Entities: ${this.stats.upgradableEntities}`);
            console.log(`   Total Upgrades Performed: ${this.stats.upgradesPerformed}`);
            console.log(`   Errors: ${this.stats.errors}`);

            return true;

        } catch (error) {
            console.log(`❌ Auto-upgrade process failed: ${error}`);
            return false;
        }
    }
}

// Global instance
let entityUpgrader = null;

// Initialize and expose global functions
Il2Cpp.perform(() => {
    entityUpgrader = new EntityAutoUpgrader();
    
    // Auto-initialize
    entityUpgrader.initialize().then(success => {
        if (success) {
            console.log("✅ EntityController Auto-Upgrader ready!");
            console.log("💡 Use: entityUpgrader.autoUpgradeAll() to upgrade all selected entities");
        } else {
            console.log("❌ EntityController Auto-Upgrader initialization failed");
        }
    });
});

// Export for global access
global.entityUpgrader = entityUpgrader;

// Convenient global functions
global.autoUpgradeAll = () => {
    if (entityUpgrader) {
        return entityUpgrader.autoUpgradeAll();
    } else {
        console.log("❌ EntityController Auto-Upgrader not available");
        return false;
    }
};

global.enableRvaFallback = () => {
    if (entityUpgrader) {
        entityUpgrader.setRvaFallback(true);
    } else {
        console.log("❌ EntityController Auto-Upgrader not available");
    }
};

global.disableRvaFallback = () => {
    if (entityUpgrader) {
        entityUpgrader.setRvaFallback(false);
    } else {
        console.log("❌ EntityController Auto-Upgrader not available");
    }
};

global.getUpgradeStats = () => {
    if (entityUpgrader) {
        return entityUpgrader.stats;
    } else {
        console.log("❌ EntityController Auto-Upgrader not available");
        return null;
    }
};

console.log("🔧 EntityController Auto-Upgrader script loaded");
console.log("💡 Available commands:");
console.log("   autoUpgradeAll() - Upgrades all selected entities to max level");
console.log("   enableRvaFallback() - Enable RVA-based method calling");
console.log("   disableRvaFallback() - Disable RVA-based method calling");
console.log("   getUpgradeStats() - Get upgrade statistics");
